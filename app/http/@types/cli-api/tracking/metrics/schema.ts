import { Type } from '@sinclair/typebox';

import type { TrackingMetricsResponse as Response } from '@app/http/controllers/tracking/responses/TrackingMetricsResponse';
import type { FastifyReplyTypebox, FastifyRequestTypebox } from '../../cross-cutting/schema';

const paramsSchema = Type.Object({});

const querySchema = Type.Object({ eventId: Type.String() });

const errorResponseSchema = Type.Object({ message: Type.String() });

const successResponseSchema = Type.Object({
  allEventTrackings: Type.Number(),
  eventTrackingsForTheLast15Minutes: Type.Number(),
});

const responseSchema = {
  '2xx': successResponseSchema,
  '4xx': errorResponseSchema,
  '5xx': errorResponseSchema,
};

export const trackingMetricsSchema = {
  params: paramsSchema,
  querystring: querySchema,
  response: responseSchema,
};

type Schema = typeof trackingMetricsSchema;

export type TrackingMetricsRequest = FastifyRequestTypebox<Schema>;

export type TrackingMetricsReply = FastifyReplyTypebox<Schema>;

export type TrackingMetricsResponse = ReturnType<ReturnType<typeof Response>['execute']>;


