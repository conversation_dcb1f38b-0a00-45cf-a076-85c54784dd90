import {
  FvDate,
  Maybe,
} from '@discocil/fv-domain-library/domain';
import { singleton } from 'tsyringe';

import { isObjectEmpty } from '@/cross-cutting/domain/helpers/objects';
import { FingerPrint } from '@/cross-cutting/infrastructure/middlewares/fingerPrint/contracts/FingerPrint';
import { EventTracking } from '@/tracking/domain/entities/EventTracking';
import { EEventChannel, EventType } from '@/tracking/domain/value-objects/EventType';

import type {
  EMicrositeContainerType,
  TrackingFb,
  TrackingItemPrimitive,
  TrackingUser,
} from '@/tracking/domain/contracts/EntityContracts';
import type { TrackingEventContentDto, TrackingEventDto } from '@/tracking/domain/contracts/TrackingEventUseCaseContracts';
import type { AddToCartPrimitives } from '@/tracking/domain/entities/AddToCart';
import type { InitiateCheckoutPrimitives } from '@/tracking/domain/entities/InitiateCheckout';
import type { PageViewPrimitives } from '@/tracking/domain/entities/PageView';
import type { PurchasePrimitives } from '@/tracking/domain/entities/Purchase';
import type { ViewContentPrimitives } from '@/tracking/domain/entities/ViewContent';
import type {
  AddToCartRequest,
  InitiateCheckoutRequest,
  PageViewRequest,
  PurchaseRequest,
  ViewContentRequest,
} from '@app/http/@types/cli-api/tracking/track/metaBodySchema';
import type { TrackingRequest } from '@app/http/@types/cli-api/tracking/track/schema';
import type {
  ECurrency,
  EMicrositeServices,
  IdPrimitive,
  MoneyProps,
} from '@discocil/fv-domain-library/domain';

type TrackingEventPrimitives = PageViewPrimitives | ViewContentPrimitives | AddToCartPrimitives | InitiateCheckoutPrimitives | PurchasePrimitives;
type TrackingEventRequest = PageViewRequest | ViewContentRequest | AddToCartRequest | InitiateCheckoutRequest | PurchaseRequest;

export type TrackingEventFingerPrint = {
  readonly remoteAddress: string;
  readonly userAgent: string;
};

@singleton()
export class ParseTrackingRequest {
  static parser(request: TrackingRequest): TrackingEventDto {
    const eventTypeOrError = EventType.build(request.body.event_name);

    if (eventTypeOrError.isLeft()) {
      throw eventTypeOrError.value;
    }

    const eventType = eventTypeOrError.value;

    const eventDto = this.buildEvent(eventType, request);

    const trackingEventDto: TrackingEventDto = {
      channel: request.params.channel ?? EEventChannel.meta,
      ...eventDto,
      externalId: eventDto.externalId.map(item => item),
      user: eventDto.user.map(item => item),
      fb: eventDto.fb.map(item => item),
      route: 'route' in eventDto ? Maybe.some(eventDto.route) : Maybe.none<string>(),
      content: 'content' in eventDto ? Maybe.some(eventDto.content) : Maybe.none<TrackingEventContentDto>(),
      items: 'items' in eventDto ? Maybe.some(eventDto.items) : Maybe.none<TrackingItemPrimitive[]>(),
      price: 'price' in eventDto ? Maybe.some(eventDto.price) : Maybe.none<MoneyProps>(),
      numItems: 'numItems' in eventDto ? Maybe.some(eventDto.numItems) : Maybe.none<number>(),
      totalPrice: 'totalPrice' in eventDto ? Maybe.some(eventDto.totalPrice) : Maybe.none<MoneyProps>(),
      eventId: Maybe.none<IdPrimitive>(),
      sessionId: Maybe.none<string>(),
      serviceType: Maybe.none<EMicrositeServices>(),
      containerType: Maybe.none<EMicrositeContainerType>(),
    };

    return trackingEventDto;
  };

  private static buildEvent(eventType: EventType, request: TrackingRequest): TrackingEventPrimitives {
    const fingerPrint = this.parseFingerPrintRequest(request.fingerPrint.fingerPrint);

    if (eventType.isViewContent()) {
      return this.parseViewContentRequest(request.body as ViewContentRequest, fingerPrint);
    }

    if (eventType.isAddToCart()) {
      return this.parseAddToCartRequest(request.body as AddToCartRequest, fingerPrint);
    }

    if (eventType.isInitiateCheckout()) {
      return this.parseInitiateCheckoutRequest(request.body as InitiateCheckoutRequest, fingerPrint);
    }

    if (eventType.isPurchase()) {
      return this.parsePurchaseRequest(request.body as PurchaseRequest, fingerPrint);
    }

    return this.parsePageViewRequest(request.body as PageViewRequest, fingerPrint);
  }

  private static parseUserRequest(bodyRequest: TrackingEventRequest): Maybe<TrackingUser> {
    const isEmpty = isObjectEmpty({
      name: bodyRequest.user_name,
      email: bodyRequest.user_email,
      phone: bodyRequest.user_phone,
    });

    if (isEmpty) {
      return Maybe.none<TrackingUser>();
    }

    return Maybe.some<TrackingUser>({
      name: Maybe.fromValue(bodyRequest.user_name),
      email: Maybe.fromValue(bodyRequest.user_email),
      phone: Maybe.fromValue(bodyRequest.user_phone),
    });
  }

  private static parseFbRequest(bodyRequest: TrackingEventRequest): Maybe<TrackingFb> {
    const isEmpty = isObjectEmpty({
      fbclid: bodyRequest.fbclid,
      fbc: bodyRequest.fbc,
      fbp: bodyRequest.fbp,
    });

    if (isEmpty) {
      return Maybe.none<TrackingFb>();
    }

    return Maybe.some<TrackingFb>({
      fbclid: Maybe.fromValue(bodyRequest.fbclid),
      fbc: Maybe.fromValue(bodyRequest.fbc),
      fbp: Maybe.fromValue(bodyRequest.fbp),
    });
  }

  private static parseMoneyRequest(amount: number, currency: string): MoneyProps {
    return {
      amount,
      currency: currency as ECurrency,
    };
  }

  private static parseFingerPrintRequest(fingerPrint: FingerPrint): TrackingEventFingerPrint {
    return {
      remoteAddress: fingerPrint.ip,
      userAgent: fingerPrint.browser.source,
    };
  }

  private static parsePageViewRequest(
    bodyRequest: PageViewRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): PageViewPrimitives {
    return {
      name: EventType.PageView().toPrimitive(),
      id: bodyRequest.event_id,
      externalId: Maybe.fromValue(bodyRequest.external_id),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      route: bodyRequest.route,
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      eventId: Maybe.fromValue(bodyRequest.event_id),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
      ...EventTracking.makeDefaultStamps(),
    };
  }

  private static parseViewContentRequest(
    bodyRequest: ViewContentRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): ViewContentPrimitives {
    return {
      name: EventType.ViewContent().toPrimitive(),
      id: bodyRequest.event_id,
      externalId: Maybe.fromValue(bodyRequest.external_id),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      content: {
        date: FvDate.createFromSeconds(bodyRequest.content_date).toPrimitive(),
        name: bodyRequest.content_name,
      },
      eventId: Maybe.fromValue(bodyRequest.event_id),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
      ...EventTracking.makeDefaultStamps(),
    };
  }

  private static parseAddToCartRequest(
    bodyRequest: AddToCartRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): AddToCartPrimitives {
    return {
      name: EventType.AddToCart().toPrimitive(),
      id: bodyRequest.event_id,
      externalId: Maybe.fromValue(bodyRequest.external_id),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      content: {
        id: bodyRequest.content_id,
        date: FvDate.createFromSeconds(bodyRequest.content_date).toPrimitive(),
        name: bodyRequest.content_name,
        type: bodyRequest.content_type,
        ids: bodyRequest.content_ids,
      },
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      numItems: bodyRequest.num_items,
      totalPrice: this.parseMoneyRequest(bodyRequest.value, bodyRequest.currency),
      items: bodyRequest.items.map(item => ({
        id: item.id,
        price: this.parseMoneyRequest(item.price, bodyRequest.currency).amount,
        quantity: item.quantity,
        category: item.category,
      })),
      eventId: Maybe.none(),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
      ...EventTracking.makeDefaultStamps(),
    };
  }

  private static parseInitiateCheckoutRequest(
    bodyRequest: InitiateCheckoutRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): InitiateCheckoutPrimitives {
    return {
      name: EventType.InitiateCheckout().toPrimitive(),
      id: bodyRequest.event_id,
      externalId: Maybe.fromValue(bodyRequest.external_id),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      content: {
        id: bodyRequest.content_id,
        name: bodyRequest.content_name,
        type: bodyRequest.content_type,
        ids: bodyRequest.content_ids,
      },
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      numItems: bodyRequest.num_items,
      totalPrice: this.parseMoneyRequest(bodyRequest.value, bodyRequest.currency),
      items: bodyRequest.items.map(item => ({
        id: item.id,
        price: this.parseMoneyRequest(item.price, bodyRequest.currency).amount,
        quantity: item.quantity,
        category: item.category,
      })),
      eventId: Maybe.none(),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
      ...EventTracking.makeDefaultStamps(),
    };
  }

  private static parsePurchaseRequest(
    bodyRequest: PurchaseRequest,
    fingerPrint: TrackingEventFingerPrint,
  ): PurchasePrimitives {
    return {
      name: EventType.Purchase().toPrimitive(),
      id: bodyRequest.event_id,
      externalId: Maybe.fromValue(bodyRequest.external_id),
      organizationId: bodyRequest.organization_id,
      urlPage: bodyRequest.url_page,
      user: this.parseUserRequest(bodyRequest),
      fb: this.parseFbRequest(bodyRequest),
      content: {
        id: bodyRequest.content_id,
        name: bodyRequest.content_name,
        type: bodyRequest.content_type,
        ids: bodyRequest.content_ids,
      },
      price: this.parseMoneyRequest(bodyRequest.price, bodyRequest.currency),
      numItems: bodyRequest.num_items,
      totalPrice: this.parseMoneyRequest(bodyRequest.value, bodyRequest.currency),
      items: bodyRequest.items.map(item => ({
        id: item.id,
        price: this.parseMoneyRequest(item.price, bodyRequest.currency).amount,
        quantity: item.quantity,
        category: item.category,
      })),
      eventId: Maybe.none(),
      sessionId: Maybe.none(),
      serviceType: Maybe.none(),
      containerType: Maybe.none(),
      ...fingerPrint,
      ...EventTracking.makeDefaultStamps(),
    };
  }
}
