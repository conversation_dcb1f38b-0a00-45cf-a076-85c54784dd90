import { HTTP_CODES } from '@app/http/HttpCodes';

import type { TrackingMetricsFvEventResponse } from '@/tracking/domain/entities/EventTracking';
import type { successResponseSchema } from '@app/http/@types/cli-api/tracking/metrics/successResponseSchema';
import type { IResponse } from '@app/http/responses/ResponseContracts';
import type { Static } from '@sinclair/typebox';

type Response = Static<typeof successResponseSchema>;

export const TrackingMetricsResponse = (): IResponse<TrackingMetricsFvEventResponse, Response> => {
  const execute = (dto: TrackingMetricsFvEventResponse): Response => {
    const { allEventTrackings, eventTrackingsForTheLast15Minutes } = dto;

    return {
      allEventTrackings: allEventTrackings.size,
      eventTrackingsForTheLast15Minutes: eventTrackingsForTheLast15Minutes.size,
    };
  };

  return {
    execute,
    status: (): HTTP_CODES => HTTP_CODES.OK_200,
  };
};
