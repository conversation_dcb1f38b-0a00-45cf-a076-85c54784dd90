import { singleton } from 'tsyringe';

import { trackingMetricsSchema } from '../@types/cli-api/tracking/metrics/schema';
import { trackingTypeSchema } from '../@types/cli-api/tracking/track/schema';
import { TrackingController } from '../controllers/tracking/TrackingController';
import { TrackingMetricsController } from '../controllers/tracking/TrackingMetricsController';

import type { FastifyInstance } from 'fastify';

@singleton()
export class TrackingRoutes {
  constructor(
    private readonly trackingController: TrackingController,
    private readonly tracingMetricsController: TrackingMetricsController,
  ) {}

  async setup(server: FastifyInstance): Promise<void> {
    server.post('/:channel', {
      schema: {
        tags: ['Trackings'],
        description: 'Tracking actions',
        ...trackingTypeSchema,
      },
      handler: this.trackingController.handler.bind(this.trackingController),
    });

    server.get('/metrics', {
      schema: {
        tags: ['Trackings'],
        description: 'Tracking metrics',
        ...trackingMetricsSchema,
      },
      handler: this.trackingController.handler.bind(this.tracingMetricsController),
    });
  }
}
